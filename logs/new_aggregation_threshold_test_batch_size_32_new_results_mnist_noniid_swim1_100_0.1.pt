PK                    \  new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim1_100_0.1/data.pklFB ZZ�}q (X   final_accuracyqG@X���
=qX
   final_lossqG?��,��P'X   accuracy_historyq]q(G@C�Q��G@Fnz�G�G@R�=p��
G@Q��
=p�G@V�\(�G@V.z�G�G@W0     G@VU\(�G@U������G@VZ=p��
G@V;��Q�G@V��RG@W�p��
=G@W�z�G�G@W�
=p��G@W,(�\G@XHQ��G@X
=p��G@W�\(��G@X:=p��
G@X�\)G@XJ�G�{G@X,(�\G@W��Q�G@W�p��
=G@X)�����G@Xq��RG@W:=p��
G@Xj=p��
G@X��Q�G@XJ=p��
G@XU\(�G@X�33333G@Xc33333G@X�p��
=G@X\(�G@XTz�G�G@X{��Q�G@X3�
=p�G@Xa��RG@X��z�HG@X<(�\G@W��Q�G@X���RG@Xb�\(��G@XX�\)G@X�\(�G@XH�\)G@XK��Q�G@X�\(�G@X������G@X��Q�G@X�z�G�G@Xp��
=qG@X�
=p��G@X�\(�G@X�p��
=G@X�(�\G@X�G�z�G@Xz=p��
G@X���Q�G@XI�����G@Xb�\(��G@X�     G@X��\(��G@X�G�z�G@X�z�G�G@X���RG@X��Q�G@X��G�{G@X������G@X���Q�G@XffffffG@X�     G@X��
=p�G@X��
=p�G@X�Q��G@X���
=qG@X�G�z�G@X��\)G@X��Q�G@X�fffffG@X�
=p��G@Xk��Q�G@X�(�\G@X�z�G�G@X�     G@X�fffffG@X|�����G@Xp��
=qG@X��z�HG@Xtz�G�G@X�\(�G@X�G�z�G@X�G�z�G@X��
=p�G@X�G�z�G@X�
=p��G@X��Q�G@X���
=qeX   configq}q(X   seedqK X   deviceqX   cudaq	X   datadirq
X   ./dataqX   logdirqX   ./logsq
X   datasetqX   mnistqX	   partitionqX   noniidqX   betaqG?ə�����X	   n_clientsqKdX	   cfractionqG?�������X   local_epochsqK
X   comm_roundsqKdX   lrqG?�z�G�{X   train_batch_sizeqK@X   test_batch_sizeqK X   modelqX   resnetqX
   model_versionqX	   optimizedqX   use_swimqKX   out_dimqM X   temperatureq G?�      X   model_buffer_sizeq!KX   krq"G?ٙ�����X   async_weight_strategyq#X   local_roundsq$uu.PK���;    PK                    c , new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim1_100_0.1/.format_versionFB( ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ1PK��܃      PK                    f + new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim1_100_0.1/.storage_alignmentFB' ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ64PK?wq�      PK                    ] 3 new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim1_100_0.1/byteorderFB/ ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZlittlePK�=�      PK                    [ 1 new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim1_100_0.1/versionFB- ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ3
PKўgU      PK                    j & new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim1_100_0.1/.data/serialization_idFB" ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ0290846867776292310400062911058856891579PK�rv�(   (   PK          ���;    \                 new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim1_100_0.1/data.pklPK          ��܃      c             �  new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim1_100_0.1/.format_versionPK          ?wq�      f             Q  new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim1_100_0.1/.storage_alignmentPK          �=�      ]               new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim1_100_0.1/byteorderPK          ўgU      [             �  new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim1_100_0.1/versionPK          �rv�(   (   j             �	  new_aggregation_threshold_test_batch_size_32_new_results_mnist_noniid_swim1_100_0.1/.data/serialization_idPK,       -                       [      x
      PK    �
         PK      [  x
    
PK                    E  test_batch_size_32_new_results_cifar100_noniid_swim1_100_0.1/data.pklFB ZZZZZZZZZZZZZZZZZZZZZZZZZ�}q (X   final_accuracyqG@:8Q��X
   final_lossqG@�'V�L�X   accuracy_historyq]q(G?�      G?�      G?�      G?�      G?�G�z�HG?�      G?�Q��RG?�������G?�\(��G?�      G@\(��G?�z�G�{G@��Q�G@ ��
=p�G@Q��RG?�=p��
=G@
=p��
G?�=p��
=G@�z�G�G?�ffffffG?�      G@
�G�z�G@�
=p��G@
�Q�G@\(�\G@p��
=qG@��Q�G@(�\)G@!�
=p��G@��
=p�G@Q��RG?�Q��RG@%p��
=qG@$p��
=qG@�Q�G?�      G@������G?�G�z�HG@%k��Q�G@&�fffffG@#\(��G?�      G@)Q��RG@&��Q�G@&���RG@'�
=p��G@�\(�G@�Q�G@/#�
=p�G@1@     G@(�Q�G@)\(�G@1B�\(��G@������G@-u\(�G@)aG�z�G@0��z�HG@1=p��
=G@(z�G�G@2nz�G�G@5��RG@4c�
=p�G@ u\(�G@0�     G@,�fffffG@6�\(�G@6B�\(��G@6(�\G@7�z�HG@70��
=qG@3Q��RG@6�     G@6�\(�G@8#�
=p�G@8���RG@6E�Q�G@:��
=p�G@-Ǯz�HG@9��\)G@6Y�����G@5xQ��G@7������G@9z�G�{G@5G�z�HG@8p��
=qG@;�Q�G@:nz�G�G@;&fffffG@:��G�{G@6333333G@:�z�HG@;�fffffG@=�\(�G@=J=p��
G@<p��
=qG@:aG�z�G@<�     G@<���
=qG@;�\(�G@:8Q��eX   configq}q(X   seedqK X   deviceqX   cudaq	X   datadirq
X   ./dataqX   logdirqX   ./logsq
X   datasetqX   cifar100qX	   partitionqX   noniidqX   betaqG?ə�����X	   n_clientsqKdX	   cfractionqG?�������X   local_epochsqK
X   comm_roundsqKdX   lrqG?�������X   train_batch_sizeqK@X   test_batch_sizeqK X   modelqX   resnet50qX   use_swimqKX   out_dimqM X   temperatureqG?�      X   model_buffer_sizeqKX   krq G?ٙ�����X   async_weight_strategyq!X   local_roundsq"uu.PK��7W�  �  PK                    L " test_batch_size_32_new_results_cifar100_noniid_swim1_100_0.1/.format_versionFB ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ1PK��܃      PK                    O B test_batch_size_32_new_results_cifar100_noniid_swim1_100_0.1/.storage_alignmentFB> ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ64PK?wq�      PK                    F 
 test_batch_size_32_new_results_cifar100_noniid_swim1_100_0.1/byteorderFB ZZZZZZlittlePK�=�      PK                    D  test_batch_size_32_new_results_cifar100_noniid_swim1_100_0.1/versionFB ZZZZ3
PKўgU      PK                    S = test_batch_size_32_new_results_cifar100_noniid_swim1_100_0.1/.data/serialization_idFB9 ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ0290846867776292310400070277904908235068PK��[�(   (   PK          ��7W�  �  E                 test_batch_size_32_new_results_cifar100_noniid_swim1_100_0.1/data.pklPK          ��܃      L             t  test_batch_size_32_new_results_cifar100_noniid_swim1_100_0.1/.format_versionPK          ?wq�      O               test_batch_size_32_new_results_cifar100_noniid_swim1_100_0.1/.storage_alignmentPK          �=�      F             �  test_batch_size_32_new_results_cifar100_noniid_swim1_100_0.1/byteorderPK          ўgU      D             V  test_batch_size_32_new_results_cifar100_noniid_swim1_100_0.1/versionPK          ��[�(   (   S             �  test_batch_size_32_new_results_cifar100_noniid_swim1_100_0.1/.data/serialization_idPK,       -                       �      �	      PK    �         PK      �  �	    
# SWIM-APPAFL集成服务器实现
# 实现APPAFL的异步联邦学习服务器端聚合机制
import numpy as np
import torch
import torch.nn.functional as F
import copy
import random


class APPAFLServer(object):
    """
    APPAFL异步联邦学习服务器
    支持延迟客户端处理和加权聚合
    """
    def __init__(self, model, device, n_clients, cfraction=0.5):
        """
        初始化服务器
        Args:
            model: 全局模型
            device: 计算设备
            n_clients: 客户端总数
            cfraction: 每轮参与的客户端比例
        """
        self.global_model = model
        self.device = device
        self.n_clients = n_clients
        self.cfraction = cfraction
        
        # 计算每轮参与的客户端数量
        self.n_clients_per_round = int(max(n_clients * cfraction, 1))
        
        # 原始APPAFL异步学习参数
        self.percentage_of_stale = 0.4  # 延迟客户端比例（与原始APPAFL一致）
        self.stale_threshold = 4        # 每个客户端延迟的阈值（与原始APPAFL一致）
        self.aggregation_threshold = 6  # 陈旧模型聚合阈值（与原始APPAFL一致）
        
        # 客户端状态跟踪
        self.client_flags = [1] * n_clients          # 客户端参与训练标志
        self.client_weights = [1.0] * n_clients      # 客户端聚合权重
        self.client_stale_times = [0] * n_clients    # 客户端延迟轮次
        self.client_timestamps = [0] * n_clients     # 客户端时间戳
        
        # 全局模型参数
        self.global_parameters = {}
        self.stale_global_parameters = {}
        
        # 初始化全局模型参数
        for key, var in self.global_model.state_dict().items():
            self.global_parameters[key] = var.clone()
            self.stale_global_parameters[key] = var.clone()

    def flag_update(self, round_num, clients_in_comm):
        """
        更新客户端参与标志的函数（与原始APPAFL完全一致）
        Args:
            round_num: 当前训练轮次
            clients_in_comm: 参与本轮通信的客户端列表
        """
        # num = self.client_flags.count(0)  # 统计当前轮次中有几个延迟客户端
        # if num == 0:
        #     # 随机生成指定数目延迟客户端的索引（从参与训练的客户端中选择）
        #     target_stale_num = int(len(clients_in_comm) * self.percentage_of_stale - num)
        #     if target_stale_num > 0:
        #         # 从参与训练的客户端中随机选择延迟客户端
        #         index = np.random.permutation(len(clients_in_comm))[0:target_stale_num]
        #         # 对于上一轮没有延迟的客户，将延迟标志设为0，时间戳设为上一轮次
        #         for j in range(len(index)):
        #             client_id = clients_in_comm[index[j]]
        #             if self.client_flags[client_id] != 0:
        #                 self.client_flags[client_id] = 0          # 设置为延迟状态
        #                 self.client_timestamps[client_id] = round_num # 记录开始延迟的轮次
        num = self.client_flags.count(0)  # 统计当前轮次中有几个延迟客户端
        if num == 0:
            # 随机生成指定数目延迟客户端的索引（从参与训练的客户端中选择）
            num_in_comm = len(clients_in_comm)
            index = np.random.permutation(num_in_comm)[0:int(num_in_comm * self.percentage_of_stale - num)]
            # 对于上一轮没有延迟的客户，将延迟标志设为0，时间戳设为上一轮次
            for j in range(len(index)):
                client_id = clients_in_comm[index[j]]
                if self.client_flags[client_id] != 0:
                    self.client_flags[client_id] = 0  # 设置为延迟状态
                    self.client_timestamps[client_id] = round_num  # 记录开始延迟的轮次

    def stale_time_update(self):
        """
        更新客户端延迟时间的函数（与原始APPAFL完全一致）
        """
        index = [i for i, x in enumerate(self.client_flags) if x == 0]  # 获取延迟客户端的索引
        for j in range(len(index)):
            # 如果该客户端上一轮没有延迟则设置延迟轮数，如果上一轮延迟则不做更改
            if self.client_stale_times[index[j]] == 0:
                # 随机确定客户端延迟的轮数（1到stale_threshold之间）
                self.client_stale_times[index[j]] = np.random.permutation(self.stale_threshold)[0] + 1

    def update_client_flags(self, round_num, clients_in_comm):
        """
        更新客户端参与标志和延迟时间（使用原始APPAFL逻辑）
        Args:
            round_num: 当前通信轮次
            clients_in_comm: 参与本轮通信的客户端列表
        """
        if round_num == 0:
            return

        # 使用原始APPAFL的更新逻辑
        self.flag_update(round_num, clients_in_comm)
        self.stale_time_update()

        print("客户端的延迟轮次为：{}".format(self.client_stale_times))

    def select_clients(self, round_num):
        """
        选择参与本轮训练的客户端（使用原始APPAFL逻辑）
        Args:
            round_num: 当前通信轮次
        Returns:
            tuple: (训练客户端列表, 使用陈旧模型的客户端列表)
        """
        # 随机选择参与本轮训练的客户端（与原始APPAFL一致）
        order = np.random.permutation(self.n_clients)
        clients_in_comm = [int(i) for i in order[0:self.n_clients_per_round]]  # 确保是Python int类型

        # 更新客户端状态（传入参与通信的客户端列表）
        self.update_client_flags(round_num, clients_in_comm)

        training_clients = []  # 实际参与训练的客户端
        stale_model_clients = []  # 使用陈旧模型的客户端

        if round_num != 0:
            # 处理正常客户端（flag=1）
            id = [key for key, value in enumerate(self.client_flags) if value == 1]  # 获取正常客户端的索引
            for m in range(len(id)):
                self.client_weights[id[m]] = 1  # 设置正常客户端权重为1
                if id[m] in clients_in_comm:
                    training_clients.append(int(id[m]))

            # 处理延迟客户端（flag=0）
            iid = [key for key, value in enumerate(self.client_flags) if value == 0]  # 获取延迟客户端的索引
            s = []  # 存放本轮结束延迟、重新参与训练的客户端索引

            for m in range(len(iid)):
                self.client_stale_times[iid[m]] -= 1  # 延迟时间减1

                # 如果延迟时间结束，客户端重新参与训练
                if self.client_stale_times[iid[m]] == 0:
                    s.append(iid[m])
                    self.client_flags[iid[m]] = 1  # 恢复参与状态

                    if iid[m] in clients_in_comm:
                        training_clients.append(int(iid[m]))

                        # 如果延迟轮数超过阈值，则不参与聚合
                        if (round_num + 1) - self.client_timestamps[iid[m]] > self.aggregation_threshold:
                            self.client_weights[iid[m]] = 0.0
                        else:
                            # 使用指数衰减权重：权重 = 2^(-延迟轮数)
                            self.client_weights[iid[m]] = float(
                                2 ** (-(round_num + 1 - self.client_timestamps[iid[m]])))

                        print('客户{}的权重分数为{}'.format(iid[m], self.client_weights[iid[m]]))

            print("参与此轮训练的延迟客户端：{}".format(s))
            print("未参与此轮训练的延迟客户端：{}".format(list(set(iid) - set(s))))

            # 处理未参与训练但可以使用陈旧模型的客户端
            if round_num < 100:  # 在前100轮中允许使用陈旧模型
                uu = list(set(iid) - set(s))  # 仍在延迟中的客户端
                for m in range(len(uu)):
                    # 如果延迟轮数不超过阈值，使用该客户端的陈旧模型
                    if (round_num + 1) - self.client_timestamps[uu[m]] <= self.aggregation_threshold:
                        self.client_weights[uu[m]] = float(
                            2 ** (-(round_num + 1 - self.client_timestamps[uu[m]])))  # 指数衰减权重
                        stale_model_clients.append(int(uu[m]))
                        print("{}的权重分数为{}(此轮未到达但是用该客户端的陈旧模型)".format(uu[m],
                                                                                            self.client_weights[uu[m]]))
                    else:
                        print("此轮未使用客户端{}的陈旧模型参与聚合".format(uu[m]))
            else:
                stale_model_clients = []  # 100轮后不再使用陈旧模型
        else:
            # 第一轮所有选中的客户端都参与训练
            training_clients = [int(client_id) for client_id in clients_in_comm]
        print("参与此轮训练的所有客户端：{}".format(training_clients + stale_model_clients))
        return training_clients, stale_model_clients

    def aggregate_models(self, client_models, client_ids, round_num):
        """
        聚合客户端模型
        Args:
            client_models: 客户端模型参数列表
            client_ids: 客户端ID列表
            round_num: 当前通信轮次
        Returns:
            dict: 聚合后的全局模型参数
        """
        if not client_models:
            return self.global_parameters

        # 计算客户端权重
        total_weight = 0
        valid_models = []
        valid_weights = []

        for i, client_id in enumerate(client_ids):
            # 使用预先计算的权重（在select_clients中已经设置）
            weight = self.client_weights[client_id]

            # 如果权重为0，则不参与聚合（与原始APPAFL一致）
            if weight == 0:
                print(f"  客户端{client_id}权重为0，跳过聚合")
                continue

            valid_models.append(client_models[i])
            valid_weights.append(weight)
            total_weight += weight

        # if not valid_models:
        #     print(f"警告：没有有效的客户端模型参与聚合")
        #     print(f"  总客户端: {len(client_ids)}")
        #     print(f"  客户端权重情况: {[(cid, self.client_weights[cid]) for cid in client_ids]}")
        #     return self.global_parameters
        # 如果没有有效模型，返回当前全局模型
        if not valid_models or total_weight == 0:
            return self.global_parameters

        # 标准化权重
        valid_weights = [w / total_weight for w in valid_weights]

        # 加权聚合（确保设备和数据类型一致性）
        aggregated_params = {}
        for key in valid_models[0].keys():
            # 获取参考参数的数据类型和设备
            ref_param = valid_models[0][key]
            target_dtype = ref_param.dtype

            # 对于整数类型的参数（如BatchNorm的num_batches_tracked），转换为浮点数进行计算
            if target_dtype in [torch.int64, torch.int32, torch.long]:
                target_dtype = torch.float32

            # 确保初始化张量在正确的设备上，并保持正确的数据类型
            aggregated_params[key] = torch.zeros_like(ref_param).to(self.device).to(target_dtype)

            for i, model_params in enumerate(valid_models):
                # 确保模型参数在正确的设备上，并保持正确的数据类型
                param_tensor = model_params[key].to(self.device).to(target_dtype)
                weight_tensor = torch.tensor(valid_weights[i], dtype=target_dtype, device=self.device)

                # 执行加权聚合
                aggregated_params[key] += weight_tensor * param_tensor

            # 如果原始参数是整数类型，聚合后转换回整数类型
            if ref_param.dtype in [torch.int64, torch.int32, torch.long]:
                aggregated_params[key] = aggregated_params[key].to(ref_param.dtype)

        print(f"聚合了 {len(valid_models)} 个客户端模型")
        return aggregated_params

    def get_stale_model(self, client_id, round_num):
        """
        获取客户端的陈旧模型参数
        Args:
            client_id: 客户端ID
            round_num: 当前通信轮次
        Returns:
            dict: 客户端应该使用的模型参数
        """
        # 如果是第一轮或客户端没有延迟，返回当前全局模型
        if round_num == 0 or self.client_flags[client_id] == 1:
            return self.global_parameters

        # 如果客户端有延迟，计算其应该使用的陈旧模型
        # 这里简化处理：返回当前全局模型（在实际APPAFL中可能需要保存历史模型）
        stale_rounds = round_num - self.client_timestamps[client_id]

        # 如果延迟轮数过多，仍然使用当前全局模型
        if stale_rounds > self.aggregation_threshold:
            return self.global_parameters

        # 在简化实现中，我们返回当前全局模型
        # 在完整的APPAFL实现中，这里应该返回客户端上次参与时的模型
        return self.global_parameters

    # def aggregate_models(self, client_models, client_ids, round_num):
    #     """
    #     聚合客户端模型，添加动量和平滑处理
    #     """
    #     if not client_models:
    #         return self.global_parameters
    #
    #     # 计算客户端权重
    #     total_weight = 0
    #     valid_models = []
    #     valid_weights = []
    #
    #     for i, client_id in enumerate(client_ids):
    #         # 使用预先计算的权重
    #         weight = self.client_weights[client_id]
    #
    #         # 如果权重为0，则不参与聚合
    #         if weight == 0:
    #             print(f"  客户端{client_id}权重为0，跳过聚合")
    #             continue
    #
    #         valid_models.append(client_models[i])
    #         valid_weights.append(weight)
    #         total_weight += weight
    #
    #     # 如果没有有效模型，返回当前全局模型
    #     if not valid_models or total_weight == 0:
    #         return self.global_parameters
    #
    #     # 归一化权重
    #     valid_weights = [w / total_weight for w in valid_weights]
    #
    #     # 加权聚合（确保设备和数据类型一致性）
    #     aggregated_params = {}
    #
    #     # 添加动量项
    #     momentum = 0.1  # 动量系数
    #
    #     for key in valid_models[0].keys():
    #         # 获取参考参数的数据类型和设备
    #         ref_param = valid_models[0][key]
    #         target_dtype = ref_param.dtype
    #
    #         # 对于整数类型的参数，转换为浮点数进行计算
    #         if target_dtype in [torch.int64, torch.int32, torch.long]:
    #             target_dtype = torch.float32
    #
    #         # 初始化聚合参数
    #         aggregated_params[key] = torch.zeros_like(ref_param).to(self.device).to(target_dtype)
    #
    #         for i, model_params in enumerate(valid_models):
    #             # 确保模型参数在正确的设备上，并保持正确的数据类型
    #             param_tensor = model_params[key].to(self.device).to(target_dtype)
    #             weight_tensor = torch.tensor(valid_weights[i], dtype=target_dtype, device=self.device)
    #
    #             # 执行加权聚合
    #             aggregated_params[key] += weight_tensor * param_tensor
    #
    #         # 添加动量项，平滑聚合过程
    #         if key in self.global_parameters:
    #             # 确保全局参数也在正确的设备上并具有正确的数据类型
    #             global_param = self.global_parameters[key].to(self.device).to(target_dtype)
    #             aggregated_params[key] = (1 - momentum) * aggregated_params[key] + momentum * global_param
    #
    #         # 如果原始参数是整数类型，聚合后转换回整数类型
    #         if ref_param.dtype in [torch.int64, torch.int32, torch.long]:
    #             aggregated_params[key] = aggregated_params[key].to(ref_param.dtype)
    #
    #     print(f"聚合了 {len(valid_models)} 个客户端模型")
    #     return aggregated_params





    def update_global_model(self, aggregated_params):
        """
        更新全局模型
        Args:
            aggregated_params: 聚合后的模型参数
        """
        # 保存当前全局模型为陈旧模型
        for key, var in self.global_parameters.items():
            self.stale_global_parameters[key] = var.clone()
            
        # 更新全局模型
        self.global_parameters = aggregated_params
        self.global_model.load_state_dict(aggregated_params)

    def get_model_for_client(self, client_id, is_stale=False):
        """
        为客户端获取模型参数
        Args:
            client_id: 客户端ID
            is_stale: 是否为延迟客户端
        Returns:
            dict: 模型参数
        """
        if is_stale:
            # 延迟客户端使用陈旧模型（确保在CPU上以便后续移动到正确设备）
            stale_params = copy.deepcopy(self.stale_global_parameters)
            # 将参数移动到CPU，避免设备冲突
            for key in stale_params:
                stale_params[key] = stale_params[key].cpu()
            return stale_params
        else:
            # 正常客户端使用最新全局模型（确保在CPU上以便后续移动到正确设备）
            global_params = copy.deepcopy(self.global_parameters)
            # 将参数移动到CPU，避免设备冲突
            for key in global_params:
                global_params[key] = global_params[key].cpu()
            return global_params

    def evaluate_model(self, test_dataloader):
        """
        评估全局模型性能
        Args:
            test_dataloader: 测试数据加载器
        Returns:
            tuple: (准确率, 损失)
        """
        self.global_model.eval()
        self.global_model.to(self.device)
        
        correct = 0
        total = 0
        total_loss = 0
        criterion = torch.nn.CrossEntropyLoss()
        
        with torch.no_grad():
            for data, target in test_dataloader:
                data, target = data.to(self.device), target.to(self.device)
                
                # 根据模型类型选择输出
                model_output = self.global_model(data)
                if isinstance(model_output, tuple):
                    # SWIM模型返回 (features, projection, classification)
                    # 评估时我们只需要分类输出
                    _, _, output = model_output
                else:
                    # 标准模型直接返回分类结果
                    output = model_output
                
                # 计算损失
                loss = criterion(output, target)
                total_loss += loss.item()
                
                # 计算准确率
                pred = output.argmax(dim=1, keepdim=True)
                correct += pred.eq(target.view_as(pred)).sum().item()
                total += target.size(0)
        
        accuracy = 100.0 * correct / total
        avg_loss = total_loss / len(test_dataloader)
        
        return accuracy, avg_loss

    def print_client_status(self, round_num):
        """
        打印客户端状态信息
        Args:
            round_num: 当前通信轮次
        """
        print(f"\n=== 第 {round_num + 1} 轮客户端状态 ===")
        print(f"延迟客户端: {[i for i, flag in enumerate(self.client_flags) if flag == 0]}")
        print(f"延迟轮次: {self.client_stale_times}")
        print(f"客户端时间戳: {self.client_timestamps}")
